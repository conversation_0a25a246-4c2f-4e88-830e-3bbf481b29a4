import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import imgAvatar from "figma:asset/ac54c311a46b9a8d0c42aec47df8efbda5b9b701.png";
import imgAvatar1 from "figma:asset/ec499efc7cdc80c48398b7a6237b76c0181d476d.png";
import svgPaths from "../imports/svg-k196gok41b";

interface Participant {
  id: string;
  name: string;
  initials?: string;
  avatar?: string;
  role: 'host' | 'presenter' | 'participant';
  micOn: boolean;
  videoOn: boolean;
  isYou?: boolean;
}

interface ChatMessage {
  id: string;
  sender: string;
  senderInitials: string;
  message?: string;
  file?: {
    name: string;
    type: 'pdf' | 'zip' | 'doc';
  };
  time: string;
  isOwn?: boolean;
}

export default function InteractiveVideoCall() {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [micOn, setMicOn] = useState(false);
  const [videoOn, setVideoOn] = useState(false);
  const [speakerOn, setSpeakerOn] = useState(true);
  const [isHandRaised, setIsHandRaised] = useState(false);
  const [selectedParticipant, setSelectedParticipant] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [meetingDuration, setMeetingDuration] = useState(2);
  const [showParticipantMenu, setShowParticipantMenu] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [speakingParticipants, setSpeakingParticipants] = useState<string[]>([]);

  const [participants] = useState<Participant[]>([
    { id: '1', name: 'Serag (You)', avatar: imgAvatar, role: 'host', micOn: false, videoOn: true, isYou: true },
    { id: '2', name: 'M. El Gammal', initials: 'ME', role: 'presenter', micOn: false, videoOn: false },
    { id: '3', name: 'Sara Naser', initials: 'SN', role: 'participant', micOn: true, videoOn: false },
    { id: '4', name: 'Bassam', avatar: imgAvatar1, role: 'presenter', micOn: true, videoOn: true },
    { id: '5', name: 'Ahmed', initials: 'SO', role: 'participant', micOn: false, videoOn: false },
    { id: '6', name: 'Layla', initials: 'AK', role: 'participant', micOn: true, videoOn: false },
  ]);

  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  // Timer effect for meeting duration
  useEffect(() => {
    const timer = setInterval(() => {
      setMeetingDuration(prev => prev + 1);
    }, 60000); // Update every minute
    return () => clearInterval(timer);
  }, []);

  // Simulate speaking participants
  useEffect(() => {
    const interval = setInterval(() => {
      const speakingIds = participants
        .filter(p => p.micOn && Math.random() > 0.7)
        .map(p => p.id);
      setSpeakingParticipants(speakingIds);
    }, 2000);
    
    return () => clearInterval(interval);
  }, [participants]);

  // Update unread messages when chat is closed
  useEffect(() => {
    if (!isChatOpen && chatMessages.length > 0) {
      const lastMessage = chatMessages[chatMessages.length - 1];
      if (!lastMessage.isOwn) {
        setUnreadMessages(prev => prev + 1);
      }
    } else {
      setUnreadMessages(0);
    }
  }, [chatMessages, isChatOpen]);

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}:${mins.toString().padStart(2, '0')}`;
    }
    return `0:${mins.toString().padStart(2, '0')}`;
  };

  const addMessage = () => {
    if (newMessage.trim()) {
      const now = new Date();
      const timeString = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
      
      // If this is the first message, add some sample messages first
      if (chatMessages.length === 0) {
        const sampleMessages: ChatMessage[] = [
          { 
            id: 'sample-1', 
            sender: 'M El Gammal', 
            senderInitials: 'ME',
            message: 'Let\'s discuss logistics, have the R&D, Marketing & Media all received their tasks?', 
            time: '10:30' 
          },
          { 
            id: 'sample-2', 
            sender: 'Amr K.', 
            senderInitials: 'AK',
            file: { name: 'Meeting_Files_and_Documents_3-9-24.zip', type: 'zip' },
            time: '10:31' 
          },
          { 
            id: 'sample-3', 
            sender: 'Amr K.', 
            senderInitials: 'AK',
            file: { name: 'Marketing.pdf', type: 'pdf' },
            time: '10:32' 
          },
        ];
        
        setChatMessages([...sampleMessages, {
          id: Date.now().toString(),
          sender: 'Serag F. (You)',
          senderInitials: 'SF',
          message: newMessage.trim(),
          time: timeString,
          isOwn: true
        }]);
      } else {
        setChatMessages(prev => [...prev, {
          id: Date.now().toString(),
          sender: 'Serag F. (You)',
          senderInitials: 'SF',
          message: newMessage.trim(),
          time: timeString,
          isOwn: true
        }]);
      }
      
      setNewMessage('');
    }
  };

  const StatusBar = () => (
    <div className="absolute box-border content-stretch flex h-[49px] items-center justify-between left-0 px-10 py-0 top-0 w-full">
      <div className="h-[12.576px] relative shrink-0 w-[47px]">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 47 13">
          <g>
            <path d={svgPaths.p30d10a80} fill="#F6F6F6" />
            <path d={svgPaths.p1819f800} fill="#F6F6F6" />
            <path d={svgPaths.p25ee1580} fill="#F6F6F6" />
            <path d={svgPaths.p3363f8f0} fill="#F6F6F6" />
          </g>
        </svg>
      </div>
      <div className="h-[13px] relative shrink-0 w-[78.328px]">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 79 13">
          <g>
            <rect height="12" opacity="0.35" rx="3.8" stroke="#F6F6F6" width="24" x="51.5" y="0.5" />
            <path d={svgPaths.p138cec00} fill="#F6F6F6" />
            <path d={svgPaths.p1ee02f00} fill="#F6F6F6" />
            <path clipRule="evenodd" d={svgPaths.p3305c980} fill="#F6F6F6" fillRule="evenodd" />
            <path clipRule="evenodd" d={svgPaths.p55982c0} fill="#F6F6F6" fillRule="evenodd" />
          </g>
        </svg>
      </div>
    </div>
  );

  const TopNavBar = () => (
    <div className="absolute box-border content-stretch flex gap-[18px] items-start justify-center left-[3.95%] px-0 py-2 right-[3.95%] top-[77px]">
      <div className="content-stretch flex gap-12 items-start justify-between w-full">
        <motion.div 
          className="bg-[rgba(15,15,15,0.8)] backdrop-blur-md box-border content-stretch flex gap-1 items-center justify-center p-[4px] relative rounded-[9999px] shrink-0"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <div aria-hidden="true" className="absolute border border-solid border-[rgba(255,255,255,0.1)] inset-0 pointer-events-none rounded-[9999px]" />
          <div className="content-stretch flex gap-1 items-center justify-center relative shrink-0">
            <div className="bg-[rgba(23,23,23,0.7)] backdrop-blur-sm box-border content-stretch flex gap-2 items-center justify-start pl-2 pr-4 py-2 relative rounded-[9999px] shrink-0">
              <div className="relative shrink-0 size-4">
                <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 16 16">
                  <path d={svgPaths.p9f8d300} fill="#F0F0F0" />
                </svg>
              </div>
              <div className="font-medium leading-[0] relative shrink-0 text-[16px] text-neutral-50 text-nowrap tracking-[-0.32px]">
                <p className="leading-[16px] whitespace-pre">Meeting Title</p>
              </div>
            </div>
            <div className="bg-[rgba(21,21,21,0.8)] backdrop-blur-sm box-border content-stretch flex gap-2.5 items-center justify-center px-3 py-2 relative rounded-[9999px] shrink-0">
              <div className="font-medium leading-[0] opacity-70 relative shrink-0 text-[#f0f0f0] text-[16px] text-nowrap tracking-[-0.32px]">
                <p className="leading-[16px] whitespace-pre">{formatDuration(meetingDuration)}</p>
              </div>
            </div>
          </div>
        </motion.div>
        
        <div className="content-stretch flex gap-2 items-center justify-start relative shrink-0">
          <motion.button 
            className="backdrop-blur-[8px] backdrop-filter bg-[rgba(23,23,23,0.7)] box-border content-stretch flex gap-1.5 items-center justify-center p-[10px] relative rounded-[99px] shrink-0 hover:bg-[rgba(30,30,30,0.8)] transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setSpeakerOn(!speakerOn)}
          >
            <div className={`relative shrink-0 size-5 ${speakerOn ? 'text-white' : 'text-gray-500'}`}>
              <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
                <path d={svgPaths.p3bb44a00} fill="currentColor" />
              </svg>
            </div>
          </motion.button>
          
          <motion.button 
            className="backdrop-blur-[8px] backdrop-filter bg-[rgba(23,23,23,0.7)] box-border content-stretch flex gap-1.5 items-center justify-center px-3 py-2.5 relative rounded-[99px] shrink-0 hover:bg-[rgba(30,30,30,0.8)] transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => setShowParticipantMenu(!showParticipantMenu)}
          >
            <div className="content-stretch flex gap-1.5 items-center justify-start relative shrink-0">
              <div className="relative shrink-0 size-5">
                <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
                  <path d={svgPaths.p17ba9800} fill="white" />
                </svg>
              </div>
              <div className="flex flex-col font-bold justify-center leading-[0] not-italic relative shrink-0 text-[15px] text-center text-nowrap text-white tracking-[-0.6px]">
                <p className="leading-[normal] whitespace-pre">{participants.length}</p>
              </div>
            </div>
          </motion.button>
        </div>
      </div>
    </div>
  );

  const ParticipantTile = ({ participant, isMain = false }: { participant: Participant, isMain?: boolean }) => {
    const isSelected = selectedParticipant === participant.id;
    const [isHovered, setIsHovered] = useState(false);
    const isSpeaking = speakingParticipants.includes(participant.id);
    
    return (
      <motion.div 
        className={`backdrop-blur-[6px] backdrop-filter bg-[rgba(10,10,10,0.6)] box-border content-stretch flex flex-col items-start justify-start p-[8px] relative rounded-[8px] shrink-0 cursor-pointer transition-all duration-200 group ${
          isMain ? 'size-[190px]' : 'size-[190px]'
        } ${isSelected ? 'ring-4 ring-[#dddddd]' : isSpeaking ? 'ring-2 ring-[#22c55e] animate-pulse' : 'hover:ring-2 hover:ring-[rgba(255,255,255,0.3)]'}`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => setSelectedParticipant(isSelected ? null : participant.id)}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        layoutId={participant.id}
        animate={{ 
          boxShadow: isSpeaking 
            ? "0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)" 
            : "0 4px 12px rgba(0, 0, 0, 0.4)" 
        }}
      >
        {isSelected && (
          <motion.div 
            className="absolute border-4 border-[#dddddd] border-solid inset-[-4px] pointer-events-none rounded-[12px]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          />
        )}
        
        <div className="basis-0 content-stretch flex grow items-center justify-center min-h-px min-w-px relative shrink-0 w-full">
          {participant.avatar ? (
            <div 
              className="bg-center bg-cover bg-no-repeat rounded-[9999px] shrink-0 size-20" 
              style={{ backgroundImage: `url('${participant.avatar}')` }} 
            />
          ) : (
            <div className={`relative rounded-[9999px] shrink-0 size-20 ${
              participant.role === 'host' ? 'bg-[rgba(34,255,153,0.15)]' : 
              participant.role === 'presenter' ? 'bg-[rgba(17,40,64,0.8)]' : 
              'bg-[rgba(254,55,204,0.18)]'
            }`}>
              <div className="content-stretch flex flex-col items-center justify-center overflow-clip relative size-20">
                <div className={`font-medium leading-[0] relative shrink-0 text-[32px] text-center text-nowrap tracking-[-0.04px] ${
                  participant.role === 'host' ? 'text-[#b1f1cb]' : 
                  participant.role === 'presenter' ? 'text-[#c2f3ff]' : 
                  'text-[rgba(255,211,236,0.95)]'
                }`}>
                  <p className="leading-[26px] whitespace-pre">{participant.initials}</p>
                </div>
              </div>
              {participant.role === 'host' && (
                <div aria-hidden="true" className="absolute border-[5px] border-[rgba(0,0,0,0.5)] border-solid inset-0 pointer-events-none rounded-[9999px]" />
              )}
            </div>
          )}
        </div>
        
        <motion.div 
          className={`bg-[rgba(20,20,20,0.7)] backdrop-blur-sm box-border content-stretch flex gap-2 h-[30px] items-center justify-start px-2 py-1 relative rounded-[8px] shrink-0 transition-all duration-200 ${
            isHovered ? 'bg-[rgba(30,30,30,0.8)]' : ''
          }`}
          animate={{ opacity: isHovered || isSelected ? 1 : 0.9 }}
        >
          <div className="content-stretch flex items-center justify-start relative shrink-0">
            <motion.div 
              className={`relative shrink-0 size-5 ${
                participant.micOn ? (isSpeaking ? 'text-[#22c55e]' : 'text-[#f0f0f0]') : 'text-[#6b7280]'
              }`}
              animate={{ 
                scale: isSpeaking ? [1, 1.2, 1] : participant.micOn ? [1, 1.05, 1] : 1,
              }}
              transition={{ 
                repeat: isSpeaking ? Infinity : (participant.micOn ? Infinity : 0), 
                duration: isSpeaking ? 0.8 : 1.5 
              }}
            >
              <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
                <path d={participant.micOn ? svgPaths.p98c3f80 : svgPaths.p40a9cc0} fill="currentColor" />
              </svg>
            </motion.div>
          </div>
          <div className="font-semibold leading-[0] overflow-ellipsis overflow-hidden relative shrink-0 text-[16px] text-nowrap text-[#f0f0f0] tracking-[-0.48px]">
            <p className="[text-overflow:inherit] leading-[normal] overflow-inherit whitespace-pre">{participant.name}</p>
          </div>
          {participant.role === 'host' && (
            <motion.div 
              className="content-stretch flex items-center justify-start relative shrink-0"
              animate={{ rotate: isHovered ? [0, -10, 10, 0] : 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="relative shrink-0 size-4">
                <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 18 16">
                  <path d={svgPaths.p7c98500} fill="#FEE949" fillOpacity="0.960784" />
                </svg>
              </div>
            </motion.div>
          )}
          {participant.role === 'presenter' && !participant.isYou && (
            <motion.div 
              className="content-stretch flex items-center justify-start relative shrink-0"
              animate={{ scale: isHovered ? 1.1 : 1 }}
            >
              <div className="relative shrink-0 size-4">
                <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 17 17">
                  <path d={svgPaths.p2fd19070} fill="#70B8FF" />
                </svg>
              </div>
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    );
  };

  const VideoGrid = () => (
    <div className="absolute content-stretch flex flex-col gap-4 items-center justify-center left-[17px] right-[17px] top-[140px] bottom-[200px]">
      <div className="content-stretch flex items-center justify-between relative shrink-0 w-full gap-4">
        <ParticipantTile participant={participants[0]} />
        <ParticipantTile participant={participants[1]} />
      </div>
      <div className="content-stretch flex items-center justify-between relative shrink-0 w-full gap-4">
        <ParticipantTile participant={participants[2]} isMain />
        <ParticipantTile participant={participants[3]} />
      </div>
      <div className="content-stretch flex items-center justify-between relative shrink-0 w-full gap-4">
        <ParticipantTile participant={participants[4]} />
        <ParticipantTile participant={participants[5]} />
      </div>
    </div>
  );

  const ControlButton = ({ 
    icon, 
    isActive, 
    onClick, 
    variant = 'default',
    children,
    label 
  }: { 
    icon: string, 
    isActive: boolean, 
    onClick: () => void, 
    variant?: 'default' | 'danger',
    children?: React.ReactNode,
    label?: string
  }) => {
    const [isPressed, setIsPressed] = useState(false);
    
    return (
      <motion.button
        className={`backdrop-blur-[4px] backdrop-filter box-border content-stretch flex items-center justify-center p-[16px] relative rounded-[9999px] shrink-0 transition-all duration-200 ${
          variant === 'danger' 
            ? 'bg-[#d9393f] hover:bg-[#c23339] active:bg-[#b91c1c]' 
            : isActive 
              ? 'bg-[#3b82f6] hover:bg-[#2563eb] active:bg-[#1d4ed8]' 
              : 'bg-[rgba(30,30,30,0.8)] hover:bg-[rgba(42,42,42,0.9)] active:bg-[rgba(64,64,64,0.9)]'
        } ${isPressed ? 'shadow-inner' : 'shadow-lg'}`}
        whileHover={{ 
          scale: 1.1,
          boxShadow: "0 10px 25px rgba(0,0,0,0.3)"
        }}
        whileTap={{ 
          scale: 0.9,
          boxShadow: "0 5px 10px rgba(0,0,0,0.2)"
        }}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        onMouseLeave={() => setIsPressed(false)}
        onClick={onClick}
        title={label}
      >
        <motion.div 
          className="relative shrink-0 size-7"
          animate={{ 
            rotate: isActive && variant !== 'danger' ? [0, 10, -10, 0] : 0,
            scale: isPressed ? 0.9 : 1
          }}
          transition={{ duration: 0.3 }}
        >
          <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 28 28">
            <path d={icon} fill="white" />
          </svg>
        </motion.div>
        {children}
        
        {/* Ripple effect */}
        {isPressed && (
          <motion.div
            className="absolute inset-0 rounded-[9999px] bg-white"
            initial={{ scale: 0, opacity: 0.3 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{ duration: 0.3 }}
          />
        )}
      </motion.button>
    );
  };

  const ControlBar = () => (
    <div className="absolute content-stretch flex flex-col gap-6 items-center justify-start left-0 bottom-[100px] w-full">
      <div className="bg-[rgba(23,23,23,0.7)] backdrop-blur-md box-border content-stretch flex flex-col items-center justify-center p-[16px] relative rounded-[24px] shrink-0 border border-[rgba(255,255,255,0.05)]">
        <div className="content-stretch flex gap-[16.5px] items-center justify-start relative shrink-0">
          <ControlButton 
            icon={svgPaths.p26544800} 
            isActive={videoOn} 
            onClick={() => setVideoOn(!videoOn)} 
            label={videoOn ? "Turn off camera" : "Turn on camera"}
          />
          <ControlButton 
            icon={micOn ? svgPaths.p98c3f80 : svgPaths.p1ac2c680} 
            isActive={micOn} 
            onClick={() => setMicOn(!micOn)} 
            label={micOn ? "Mute microphone" : "Unmute microphone"}
          />
          <div className="relative">
            <ControlButton 
              icon={svgPaths.pb877400} 
              isActive={isChatOpen} 
              onClick={() => setIsChatOpen(!isChatOpen)} 
              label="Toggle chat"
            />
            {unreadMessages > 0 && (
              <motion.div
                className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
              >
                {unreadMessages}
              </motion.div>
            )}
          </div>
          <ControlButton 
            icon={svgPaths.pef936c0} 
            isActive={isHandRaised} 
            onClick={() => setIsHandRaised(!isHandRaised)} 
            label={isHandRaised ? "Lower hand" : "Raise hand"}
          />
          <ControlButton 
            icon={svgPaths.pd28c800} 
            isActive={false} 
            onClick={() => {
              if (confirm('Are you sure you want to end the call?')) {
                // End call logic
                console.log('Call ended');
              }
            }} 
            variant="danger"
            label="End call"
          />
        </div>
        <div className="absolute bg-[rgba(255,255,255,0.3)] h-1 left-1/2 rounded-[100px] top-1 translate-x-[-50%] w-11 shadow-sm" />
      </div>
    </div>
  );

  const ChatOverlay = () => (
    <AnimatePresence>
      {isChatOpen && (
        <motion.div
          className="absolute inset-0 bg-black bg-opacity-60 flex items-end justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setIsChatOpen(false)}
        >
          <motion.div
            className="bg-black w-full h-full flex flex-col overflow-hidden"
            initial={{ y: "100%" }}
            animate={{ y: 0 }}
            exit={{ y: "100%" }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-[rgba(255,255,255,0.1)]">
              <div className="flex items-center gap-3">
                <button 
                  className="text-white hover:text-gray-300 transition-colors"
                  onClick={() => setIsChatOpen(false)}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <h3 className="text-white text-lg font-medium">Meeting Chat</h3>
              </div>
            </div>
            
            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4">
              {chatMessages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center">
                  <motion.div
                    className="text-6xl mb-4"
                    animate={{ 
                      rotate: [0, 10, -10, 10, 0],
                    }}
                    transition={{ 
                      duration: 1.5,
                      repeat: Infinity,
                      repeatDelay: 2
                    }}
                  >
                    👋
                  </motion.div>
                  <p className="text-gray-400 text-lg">It's quiet in here... say hi!</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {chatMessages.map((message) => (
                    <motion.div
                      key={message.id}
                      className={`flex gap-3 ${message.isOwn ? 'justify-end' : 'justify-start'}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      {!message.isOwn && (
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium shrink-0 ${
                          message.senderInitials === 'ME' ? 'bg-[#4a5f3a] text-white' :
                          message.senderInitials === 'AK' ? 'bg-[#1e3a5f] text-[#70b8ff]' :
                          'bg-[#3a1e5f] text-white'
                        }`}>
                          {message.senderInitials}
                        </div>
                      )}
                      
                      <div className={`max-w-[280px] ${message.isOwn ? 'order-first' : ''}`}>
                        {!message.isOwn && (
                          <p className="text-gray-400 text-xs mb-1 ml-1">{message.sender}</p>
                        )}
                        
                        {message.message ? (
                          <div className={`rounded-2xl px-4 py-3 ${
                            message.isOwn 
                              ? 'bg-[#fef3c7] text-black ml-auto' 
                              : 'bg-[rgba(60,60,60,0.8)] text-white'
                          }`}>
                            <p className="text-sm leading-relaxed">{message.message}</p>
                          </div>
                        ) : message.file ? (
                          <div className={`rounded-2xl px-4 py-3 flex items-center gap-3 ${
                            message.isOwn 
                              ? 'bg-[#fef3c7] text-black ml-auto' 
                              : 'bg-[rgba(60,60,60,0.8)] text-white'
                          }`}>
                            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                              message.isOwn ? 'bg-[#f59e0b]' : 'bg-[#4b5563]'
                            }`}>
                              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" />
                              </svg>
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">{message.file.name}</p>
                            </div>
                          </div>
                        ) : null}
                        
                        {message.isOwn && (
                          <p className="text-gray-400 text-xs mt-1 text-right mr-1">{message.sender}</p>
                        )}
                      </div>
                      
                      {message.isOwn && (
                        <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium shrink-0 bg-[#fef3c7] text-black">
                          {message.senderInitials}
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Input Area */}
            <div className="p-4 border-t border-[rgba(255,255,255,0.1)]">
              <div className="mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-gray-400 text-sm">To:</span>
                  <div className="flex items-center gap-1 bg-[rgba(60,60,60,0.8)] rounded-lg px-3 py-1">
                    <span className="text-white text-sm">Everyone</span>
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <button className="w-10 h-10 rounded-lg bg-[rgba(60,60,60,0.8)] flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                </button>
                
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addMessage()}
                  placeholder="Placeholder"
                  className="flex-1 bg-[rgba(60,60,60,0.8)] text-white placeholder-gray-500 rounded-lg px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 border-none"
                />
                
                <button className="w-10 h-10 rounded-lg bg-[rgba(60,60,60,0.8)] flex items-center justify-center text-gray-400 hover:text-white transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-5-8V6a1 1 0 011-1h1a1 1 0 011 1v1" />
                  </svg>
                </button>
                
                <motion.button 
                  className="w-10 h-10 rounded-lg bg-[#3b82f6] hover:bg-[#2563eb] flex items-center justify-center text-white transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={addMessage}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  const ParticipantMenu = () => (
    <AnimatePresence>
      {showParticipantMenu && (
        <motion.div
          className="absolute inset-0 bg-black bg-opacity-50 flex items-end justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setShowParticipantMenu(false)}
        >
          <motion.div
            className="bg-[rgba(26,26,26,0.95)] backdrop-blur-xl rounded-t-[24px] w-full max-h-[60%] p-6 overflow-hidden border-t border-[rgba(255,255,255,0.1)]"
            initial={{ y: "100%" }}
            animate={{ y: 0 }}
            exit={{ y: "100%" }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white text-lg font-medium">Participants ({participants.length})</h3>
              <button 
                className="text-gray-400 hover:text-white transition-colors"
                onClick={() => setShowParticipantMenu(false)}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-2 max-h-[300px] overflow-y-auto">
              {participants.map((participant) => (
                <motion.div
                  key={participant.id}
                  className="bg-[rgba(42,42,42,0.8)] backdrop-blur-sm rounded-lg p-3 flex items-center justify-between border border-[rgba(255,255,255,0.05)]"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: participants.indexOf(participant) * 0.1 }}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      participant.role === 'host' ? 'bg-[rgba(34,255,153,0.15)] text-[#b1f1cb]' : 
                      participant.role === 'presenter' ? 'bg-[rgba(17,40,64,0.8)] text-[#c2f3ff]' : 
                      'bg-[rgba(254,55,204,0.18)] text-[rgba(255,211,236,0.95)]'
                    }`}>
                      {participant.initials || participant.name.charAt(0)}
                    </div>
                    <div>
                      <p className="text-white font-medium">{participant.name}</p>
                      <p className="text-gray-400 text-xs capitalize">{participant.role}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-5 h-5 ${participant.micOn ? 'text-[#22c55e]' : 'text-[#ef4444]'}`}>
                      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 20 20">
                        <path d={participant.micOn ? svgPaths.p98c3f80 : svgPaths.p40a9cc0} />
                      </svg>
                    </div>
                    <div className={`w-5 h-5 ${participant.videoOn ? 'text-[#22c55e]' : 'text-[#ef4444]'}`}>
                      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 20 20">
                        <path d={svgPaths.p26544800} />
                      </svg>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  const PageIndicator = () => (
    <div className="absolute box-border content-stretch flex flex-col gap-2.5 items-center justify-center left-0 bottom-[40px] w-full">
      <div className="bg-[rgba(23,23,23,0.7)] backdrop-blur-md box-border content-stretch flex gap-2 items-center justify-center pl-3 pr-2 py-2 relative rounded-[36.318px] shrink-0 border border-[rgba(255,255,255,0.05)]">
        <div className="content-stretch flex gap-2 items-center justify-start relative shrink-0 w-[11px]">
          <div className="relative shrink-0 size-4">
            <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 16 16">
              <path d={svgPaths.p187fc900} fill="white" opacity="0.3" />
            </svg>
          </div>
        </div>
        <motion.div 
          className="bg-white rounded-[8px] shrink-0 size-2 cursor-pointer" 
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.8 }}
        />
        <motion.div 
          className="bg-white opacity-30 rounded-[8px] shrink-0 size-2 cursor-pointer hover:opacity-60 transition-opacity" 
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        />
      </div>
    </div>
  );

  return (
    <div className="h-screen w-full relative overflow-hidden bg-[#000000] flex items-center justify-center">
      <div className="w-full max-w-[430px] h-full relative bg-gradient-to-b from-[#1a1a1a] via-[#0f0f0f] to-[#000000]">
        <StatusBar />
        <TopNavBar />
        <VideoGrid />
        <ControlBar />
        <PageIndicator />
        <ChatOverlay />
        <ParticipantMenu />
      </div>
    </div>
  );
}