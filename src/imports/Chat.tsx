import svgPaths from "./svg-k196gok41b";
import imgAvatar from "figma:asset/ac54c311a46b9a8d0c42aec47df8efbda5b9b701.png";
import imgAvatar1 from "figma:asset/ec499efc7cdc80c48398b7a6237b76c0181d476d.png";

function Frame2085661047() {
  return (
    <div className="h-[12.576px] relative shrink-0 w-[47px]">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 47 13">
        <g id="Frame 2085661047">
          <g id="Time">
            <path d={svgPaths.p30d10a80} fill="var(--fill-0, #F6F6F6)" />
            <path d={svgPaths.p1819f800} fill="var(--fill-0, #F6F6F6)" />
            <path d={svgPaths.p25ee1580} fill="var(--fill-0, #F6F6F6)" />
            <path d={svgPaths.p3363f8f0} fill="var(--fill-0, #F6F6F6)" />
          </g>
        </g>
      </svg>
    </div>
  );
}

function Group1() {
  return (
    <div className="h-[13px] relative shrink-0 w-[78.328px]">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 79 13">
        <g id="Group 1">
          <rect height="12" id="Border" opacity="0.35" rx="3.8" stroke="var(--stroke-0, #F6F6F6)" width="24" x="51.5" y="0.5" />
          <g id="Vector">
            <path d={svgPaths.p138cec00} fill="var(--fill-0, #F6F6F6)" />
            <path d={svgPaths.p1ee02f00} fill="var(--fill-0, #F6F6F6)" />
          </g>
          <g id="Vector_2">
            <path clipRule="evenodd" d={svgPaths.p3305c980} fill="var(--fill-0, #F6F6F6)" fillRule="evenodd" />
            <path clipRule="evenodd" d={svgPaths.p55982c0} fill="var(--fill-0, #F6F6F6)" fillRule="evenodd" />
          </g>
        </g>
      </svg>
    </div>
  );
}

function StatusBar() {
  return (
    <div className="absolute box-border content-stretch flex h-[49px] items-center justify-between left-0 px-10 py-0 top-0 w-[430px]" data-name="Status Bar">
      <Frame2085661047 />
      <Group1 />
    </div>
  );
}

function Info() {
  return (
    <div className="relative shrink-0 size-4" data-name="Info">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 16 16">
        <g id="Info">
          <path d={svgPaths.p9f8d300} fill="var(--fill-0, #F0F0F0)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function Button() {
  return (
    <div className="bg-[rgba(17,17,17,0.6)] box-border content-stretch flex gap-2 items-center justify-start pl-2 pr-4 py-2 relative rounded-[9999px] shrink-0" data-name="Button">
      <Info />
      <div className="font-['Geist:Medium',_sans-serif] font-medium leading-[0] relative shrink-0 text-[16px] text-neutral-50 text-nowrap tracking-[-0.32px]">
        <p className="leading-[16px] whitespace-pre">Meeting Title</p>
      </div>
    </div>
  );
}

function Badge() {
  return (
    <div className="bg-[#151515] box-border content-stretch flex gap-2.5 items-center justify-center px-3 py-2 relative rounded-[9999px] shrink-0" data-name="Badge">
      <div className="font-['Geist:Medium',_sans-serif] font-medium leading-[0] opacity-70 relative shrink-0 text-[#f0f0f0] text-[16px] text-nowrap tracking-[-0.32px]">
        <p className="leading-[16px] whitespace-pre">0:02</p>
      </div>
    </div>
  );
}

function Frame2085661139() {
  return (
    <div className="content-stretch flex gap-1 items-center justify-center relative shrink-0">
      <Badge />
    </div>
  );
}

function Frame2085661140() {
  return (
    <div className="content-stretch flex gap-1 items-center justify-center relative shrink-0">
      <Button />
      <Frame2085661139 />
    </div>
  );
}

function Container() {
  return (
    <div className="bg-zinc-950 box-border content-stretch flex gap-1 items-center justify-center p-[4px] relative rounded-[9999px] shrink-0" data-name="Container">
      <div aria-hidden="true" className="absolute border border-solid border-zinc-800 inset-0 pointer-events-none rounded-[9999px]" />
      <Frame2085661140 />
    </div>
  );
}

function Frame2085661142() {
  return (
    <div className="content-stretch flex flex-col gap-1.5 items-start justify-center relative shrink-0">
      <Container />
    </div>
  );
}

function Speaker() {
  return (
    <div className="relative shrink-0 size-5" data-name="Speaker">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="Speaker">
          <path d={svgPaths.p3bb44a00} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function SoundOff() {
  return (
    <div className="backdrop-blur-[5px] backdrop-filter bg-[rgba(17,17,17,0.6)] box-border content-stretch flex gap-1.5 items-center justify-center p-[10px] relative rounded-[99px] shrink-0" data-name="Sound/Off">
      <Speaker />
    </div>
  );
}

function People() {
  return (
    <div className="relative shrink-0 size-5" data-name="People">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="People">
          <path d={svgPaths.p17ba9800} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function Frame2085661121() {
  return (
    <div className="content-stretch flex gap-1.5 items-center justify-start relative shrink-0">
      <People />
      <div className="flex flex-col font-['Inter:Bold',_sans-serif] font-bold justify-center leading-[0] not-italic relative shrink-0 text-[15px] text-center text-nowrap text-white tracking-[-0.6px]">
        <p className="leading-[normal] whitespace-pre">12</p>
      </div>
    </div>
  );
}

function AddOerson() {
  return (
    <div className="backdrop-blur-[5px] backdrop-filter bg-[rgba(17,17,17,0.6)] box-border content-stretch flex gap-1.5 items-center justify-center px-3 py-2.5 relative rounded-[99px] shrink-0" data-name="add oerson">
      <Frame2085661121 />
    </div>
  );
}

function Contacts() {
  return (
    <div className="content-stretch flex gap-2 items-center justify-start relative shrink-0" data-name="Contacts">
      <SoundOff />
      <AddOerson />
    </div>
  );
}

function Frame2085661143() {
  return (
    <div className="content-stretch flex gap-12 items-start justify-end relative shrink-0">
      <Frame2085661142 />
      <Contacts />
    </div>
  );
}

function TopNavBar() {
  return (
    <div className="absolute box-border content-stretch flex gap-[18px] items-start justify-center left-[3.95%] px-0 py-2 right-[3.95%] translate-y-[-50%]" data-name="top nav bar" style={{ top: "calc(50% - 372.5px)" }}>
      <Frame2085661143 />
    </div>
  );
}

function VehicleCarProfile() {
  return (
    <div className="relative shrink-0 size-4" data-name="Vehicle Car Profile">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 16 16">
        <g clipPath="url(#clip0_1_12949)" id="Vehicle Car Profile">
          <foreignObject height="210" width="214" x="-99" y="-97">
            <div style={{ backdropFilter: "blur(50px)", clipPath: "url(#bgblur_1_1_12949_clip_path)", height: "100%", width: "100%" }} xmlns="http://www.w3.org/1999/xhtml" />
          </foreignObject>
          <g data-figma-bg-blur-radius="100" filter="url(#filter0_d_1_12949)" id="Shape" opacity="0.3">
            <path d={svgPaths.p187fc900} fill="var(--fill-0, white)" />
          </g>
        </g>
        <defs>
          <filter colorInterpolationFilters="sRGB" filterUnits="userSpaceOnUse" height="210" id="filter0_d_1_12949" width="214" x="-99" y="-97">
            <feFlood floodOpacity="0" result="BackgroundImageFix" />
            <feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
            <feOffset />
            <feGaussianBlur stdDeviation="5" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0" />
            <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_1_12949" />
            <feBlend in="SourceGraphic" in2="effect1_dropShadow_1_12949" mode="normal" result="shape" />
          </filter>
          <clipPath id="bgblur_1_1_12949_clip_path" transform="translate(99 97)">
            <path d={svgPaths.p187fc900} />
          </clipPath>
          <clipPath id="clip0_1_12949">
            <rect fill="white" height="16" width="16" />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
}

function PageNodes() {
  return (
    <div className="content-stretch flex gap-2 items-center justify-start relative shrink-0 w-[11px]" data-name="Page Nodes">
      <VehicleCarProfile />
    </div>
  );
}

function PagesWhiteboard() {
  return (
    <div className="bg-[rgba(17,17,17,0.6)] box-border content-stretch flex gap-2 items-center justify-center pl-3 pr-2 py-2 relative rounded-[36.318px] shrink-0" data-name="Pages - Whiteboard">
      <PageNodes />
      <div className="bg-white rounded-[8px] shrink-0 size-2" data-name="Screen 6" />
      <div className="bg-white opacity-30 rounded-[8px] shrink-0 size-2" data-name="Screen 8" />
    </div>
  );
}

function TypesOfPages() {
  return (
    <div className="absolute box-border content-stretch flex flex-col gap-2.5 items-center justify-center left-0 pb-2 pt-0 px-0 top-[770px] w-[430px]" data-name="Types of Pages">
      <PagesWhiteboard />
    </div>
  );
}

function VideoOff() {
  return (
    <div className="relative shrink-0 size-7" data-name="Video Off">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 28 28">
        <g id="Video Off">
          <path d={svgPaths.p26544800} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function Button1() {
  return (
    <div className="backdrop-blur-[2px] backdrop-filter bg-[#1e1e1e] box-border content-stretch flex items-center justify-center p-[16px] relative rounded-[9999px] shrink-0" data-name="Button">
      <VideoOff />
    </div>
  );
}

function MicOff() {
  return (
    <div className="relative shrink-0 size-7" data-name="Mic Off">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 28 28">
        <g id="Mic Off">
          <g id="Shape">
            <path d={svgPaths.p1ac2c680} fill="var(--fill-0, white)" />
            <path d={svgPaths.p68fda00} fill="var(--fill-0, white)" />
            <path d={svgPaths.p3ae074c0} fill="var(--fill-0, white)" />
          </g>
        </g>
      </svg>
    </div>
  );
}

function Button2() {
  return (
    <div className="backdrop-blur-[2px] backdrop-filter bg-[#1e1e1e] box-border content-stretch flex items-center justify-center p-[16px] relative rounded-[9999px] shrink-0" data-name="Button">
      <MicOff />
    </div>
  );
}

function Chat() {
  return (
    <div className="relative shrink-0 size-7" data-name="Chat">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 28 28">
        <g id="Chat">
          <path d={svgPaths.pb877400} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function Button3() {
  return (
    <div className="backdrop-blur-[2px] backdrop-filter bg-[#1e1e1e] box-border content-stretch flex items-center justify-center p-[16px] relative rounded-[9999px] shrink-0" data-name="Button">
      <Chat />
    </div>
  );
}

function EmojiHand() {
  return (
    <div className="relative shrink-0 size-7" data-name="Emoji Hand">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 28 28">
        <g id="Emoji Hand">
          <path d={svgPaths.pef936c0} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function Button4() {
  return (
    <div className="backdrop-blur-[2px] backdrop-filter bg-[#1e1e1e] box-border content-stretch flex items-center justify-center p-[16px] relative rounded-[9999px] shrink-0" data-name="Button">
      <EmojiHand />
    </div>
  );
}

function CallEnd() {
  return (
    <div className="relative shrink-0 size-7" data-name="Call End">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 28 28">
        <g id="Call End">
          <path d={svgPaths.pd28c800} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function Button5() {
  return (
    <div className="backdrop-blur-[2px] backdrop-filter bg-[#d9393f] box-border content-stretch flex items-center justify-center p-[16px] relative rounded-[9999px] shrink-0" data-name="Button">
      <CallEnd />
    </div>
  );
}

function DefaultMockupButtons() {
  return (
    <div className="content-stretch flex gap-[16.5px] items-center justify-start relative shrink-0" data-name="Default Mockup buttons">
      <Button1 />
      <Button2 />
      <Button3 />
      <Button4 />
      <Button5 />
    </div>
  );
}

function Frame2085661160() {
  return <div className="absolute bg-[#e5484d] left-[215px] opacity-0 rounded-[11998.8px] size-3 top-[18px]" />;
}

function Mobile() {
  return (
    <div className="bg-[rgba(17,17,17,0.6)] box-border content-stretch flex flex-col items-center justify-center p-[16px] relative rounded-[24px] shrink-0" data-name="Mobile">
      <DefaultMockupButtons />
      <div className="absolute bg-[rgba(255,255,255,0.25)] h-1 left-1/2 rounded-[100px] top-1 translate-x-[-50%] w-11" data-name="Resize Indicator" />
      <Frame2085661160 />
    </div>
  );
}

function Version3Footer() {
  return (
    <div className="absolute content-stretch flex flex-col gap-6 items-center justify-start left-0 overflow-clip rounded-tl-[32px] rounded-tr-[32px] top-[810px] w-[430px]" data-name="Version 3 Footer">
      <Mobile />
    </div>
  );
}

function Options() {
  return <div className="content-stretch flex items-center justify-between rounded-[24px] shrink-0 w-full" data-name="Options" />;
}

function Avatar() {
  return <div className="bg-center bg-cover bg-no-repeat rounded-[9999px] shrink-0 size-20" data-name="Avatar" style={{ backgroundImage: `url('${imgAvatar}')` }} />;
}

function Avatar1() {
  return (
    <div className="basis-0 content-stretch flex grow items-center justify-center min-h-px min-w-px relative shrink-0 w-full" data-name="Avatar">
      <Avatar />
    </div>
  );
}

function MicOff1() {
  return (
    <div className="relative shrink-0 size-5" data-name="Mic Off">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="Mic Off">
          <path d={svgPaths.p40a9cc0} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function MicStatus() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Mic Status">
      <MicOff1 />
    </div>
  );
}

function Crown() {
  return (
    <div className="relative shrink-0 size-4" data-name="Crown">
      <div className="absolute bottom-0 left-[-6.25%] right-[-6.25%] top-0">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 18 16">
          <g id="Crown">
            <path d={svgPaths.p7c98500} fill="var(--fill-0, #FEE949)" fillOpacity="0.960784" id="Shape" />
          </g>
        </svg>
      </div>
    </div>
  );
}

function RoleTag() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Role Tag">
      <Crown />
    </div>
  );
}

function NameStatus() {
  return (
    <div className="bg-[rgba(30,30,30,0.6)] box-border content-stretch flex gap-2 h-[30px] items-center justify-start px-2 py-1 relative rounded-[8px] shrink-0" data-name="name & Status">
      <MicStatus />
      <div className="font-['Open_Sans:SemiBold',_sans-serif] font-semibold leading-[0] overflow-ellipsis overflow-hidden relative shrink-0 text-[16px] text-nowrap text-white tracking-[-0.48px]" style={{ fontVariationSettings: "'wdth' 100" }}>
        <p className="[text-overflow:inherit] leading-[normal] overflow-inherit whitespace-pre">Serag (You)</p>
      </div>
      <RoleTag />
    </div>
  );
}

function MobileTile() {
  return (
    <div className="backdrop-blur-[4.455px] backdrop-filter bg-[rgba(0,0,0,0.5)] box-border content-stretch flex flex-col items-start justify-start p-[8px] relative rounded-[8px] shrink-0 size-[190px]" data-name="Mobile Tile">
      <Options />
      <Avatar1 />
      <NameStatus />
    </div>
  );
}

function Options1() {
  return <div className="content-stretch flex gap-2 items-center justify-start rounded-[24px] shrink-0 w-full" data-name="Options" />;
}

function Avatar2() {
  return (
    <div className="bg-[rgba(34,255,153,0.12)] relative rounded-[9999px] shrink-0 size-20" data-name="Avatar">
      <div className="content-stretch flex flex-col items-center justify-center overflow-clip relative size-20">
        <div className="font-['Geist:Medium',_sans-serif] font-medium leading-[0] relative shrink-0 text-[#b1f1cb] text-[32px] text-center text-nowrap tracking-[-0.04px]">
          <p className="leading-[26px] whitespace-pre">ME</p>
        </div>
      </div>
      <div aria-hidden="true" className="absolute border-[5px] border-[rgba(0,0,0,0.43)] border-solid inset-0 pointer-events-none rounded-[9999px]" />
    </div>
  );
}

function Avatar3() {
  return (
    <div className="basis-0 content-stretch flex grow items-center justify-center min-h-px min-w-px relative shrink-0 w-full" data-name="Avatar">
      <Avatar2 />
    </div>
  );
}

function MicOff2() {
  return (
    <div className="relative shrink-0 size-5" data-name="Mic Off">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="Mic Off">
          <path d={svgPaths.p40a9cc0} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function MicStatus1() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Mic Status">
      <MicOff2 />
    </div>
  );
}

function PersonLightning() {
  return (
    <div className="relative shrink-0 size-4" data-name="Person Lightning">
      <div className="absolute bottom-[-6.14%] left-0 right-[-3.76%] top-0">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 17 17">
          <g id="Person Lightning">
            <path d={svgPaths.p2fd19070} fill="var(--fill-0, #70B8FF)" id="Shape" />
          </g>
        </svg>
      </div>
    </div>
  );
}

function RoleTag1() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Role Tag">
      <PersonLightning />
    </div>
  );
}

function NameStatus1() {
  return (
    <div className="bg-[rgba(30,30,30,0.6)] box-border content-stretch flex gap-2 h-[30px] items-center justify-start px-2 py-1 relative rounded-[8px] shrink-0" data-name="name & Status">
      <MicStatus1 />
      <div className="font-['Open_Sans:SemiBold',_sans-serif] font-semibold leading-[0] overflow-ellipsis overflow-hidden relative shrink-0 text-[16px] text-nowrap text-white tracking-[-0.48px]" style={{ fontVariationSettings: "'wdth' 100" }}>
        <p className="[text-overflow:inherit] leading-[normal] overflow-inherit whitespace-pre">M. El Gammal</p>
      </div>
      <RoleTag1 />
    </div>
  );
}

function MobileTile1() {
  return (
    <div className="backdrop-blur-[4.455px] backdrop-filter bg-[rgba(0,0,0,0.5)] box-border content-stretch flex flex-col items-start justify-start p-[8px] relative rounded-[8px] shrink-0 size-[190px]" data-name="Mobile Tile">
      <Options1 />
      <Avatar3 />
      <NameStatus1 />
    </div>
  );
}

function Component1() {
  return (
    <div className="content-stretch flex items-center justify-between relative shrink-0 w-full" data-name="1">
      <MobileTile />
      <MobileTile1 />
    </div>
  );
}

function Options2() {
  return <div className="content-stretch flex items-center justify-between rounded-[24px] shrink-0 w-full" data-name="Options" />;
}

function Avatar4() {
  return (
    <div className="bg-[#112840] relative rounded-[9999px] shrink-0 size-20" data-name="Avatar">
      <div className="content-stretch flex flex-col items-center justify-center overflow-clip relative size-20">
        <div className="font-['Geist:Medium',_sans-serif] font-medium leading-[0] relative shrink-0 text-[#c2f3ff] text-[32px] text-center text-nowrap tracking-[-0.04px]">
          <p className="leading-[26px] whitespace-pre">SN</p>
        </div>
      </div>
      <div aria-hidden="true" className="absolute border-[5px] border-[rgba(0,0,0,0.43)] border-solid inset-0 pointer-events-none rounded-[9999px]" />
    </div>
  );
}

function Avatar5() {
  return (
    <div className="basis-0 content-stretch flex grow items-center justify-center min-h-px min-w-px relative shrink-0 w-full" data-name="Avatar">
      <Avatar4 />
    </div>
  );
}

function Mic() {
  return (
    <div className="relative shrink-0 size-5" data-name="Mic">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="Mic">
          <path d={svgPaths.p98c3f80} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function MicStatus2() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Mic Status">
      <Mic />
    </div>
  );
}

function NameStatus2() {
  return (
    <div className="bg-[rgba(30,30,30,0.6)] box-border content-stretch flex gap-2 h-[30px] items-center justify-start px-2 py-1 relative rounded-[8px] shrink-0" data-name="name & Status">
      <MicStatus2 />
      <div className="font-['Open_Sans:SemiBold',_sans-serif] font-semibold leading-[0] overflow-ellipsis overflow-hidden relative shrink-0 text-[16px] text-nowrap text-white tracking-[-0.48px]" style={{ fontVariationSettings: "'wdth' 100" }}>
        <p className="[text-overflow:inherit] leading-[normal] overflow-inherit whitespace-pre">Sara Naser</p>
      </div>
    </div>
  );
}

function Stroke() {
  return (
    <div className="h-[190px] relative rounded-[8px] shrink-0 w-full" data-name="Stroke">
      <div aria-hidden="true" className="absolute border-2 border-black border-solid inset-0 pointer-events-none rounded-[8px]" />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col h-[190px] items-start justify-start p-[8px] relative w-full">
          <Options2 />
          <Avatar5 />
          <NameStatus2 />
        </div>
      </div>
    </div>
  );
}

function MobileTile2() {
  return (
    <div className="bg-[rgba(0,0,0,0.5)] content-stretch flex flex-col items-start justify-start relative rounded-[8px] shrink-0 w-[190px]" data-name="Mobile Tile">
      <div aria-hidden="true" className="absolute border-4 border-[#dddddd] border-solid inset-[-4px] pointer-events-none rounded-[12px]" />
      <Stroke />
    </div>
  );
}

function Options3() {
  return <div className="content-stretch flex items-center justify-between rounded-[24px] shrink-0 w-full" data-name="Options" />;
}

function Avatar6() {
  return <div className="bg-[93.6%_36.68%] bg-no-repeat bg-size-[142.47%_253.28%] rounded-[9999px] shrink-0 size-20" data-name="Avatar" style={{ backgroundImage: `url('${imgAvatar1}')` }} />;
}

function Avatar7() {
  return (
    <div className="basis-0 content-stretch flex grow items-center justify-center min-h-px min-w-px relative shrink-0 w-full" data-name="Avatar">
      <Avatar6 />
    </div>
  );
}

function Mic1() {
  return (
    <div className="relative shrink-0 size-5" data-name="Mic">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="Mic">
          <path d={svgPaths.p98c3f80} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function MicStatus3() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Mic Status">
      <Mic1 />
    </div>
  );
}

function Presenter() {
  return (
    <div className="relative shrink-0 size-4" data-name="Presenter">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 16 16">
        <g clipPath="url(#clip0_1_12813)" id="Presenter">
          <path d={svgPaths.p2e460c80} fill="var(--fill-0, #F1F7FE)" fillOpacity="0.709804" id="Shape" />
        </g>
        <defs>
          <clipPath id="clip0_1_12813">
            <rect fill="white" height="16" width="16" />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
}

function RoleTag2() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Role Tag">
      <Presenter />
    </div>
  );
}

function NameStatus3() {
  return (
    <div className="bg-[rgba(30,30,30,0.6)] box-border content-stretch flex gap-2 h-[30px] items-center justify-start px-2 py-1 relative rounded-[8px] shrink-0" data-name="name & Status">
      <MicStatus3 />
      <div className="font-['Open_Sans:SemiBold',_sans-serif] font-semibold leading-[0] overflow-ellipsis overflow-hidden relative shrink-0 text-[16px] text-nowrap text-white tracking-[-0.48px]" style={{ fontVariationSettings: "'wdth' 100" }}>
        <p className="[text-overflow:inherit] leading-[normal] overflow-inherit whitespace-pre">Bassam</p>
      </div>
      <RoleTag2 />
    </div>
  );
}

function MobileTile3() {
  return (
    <div className="backdrop-blur-[4.455px] backdrop-filter bg-[rgba(0,0,0,0.5)] box-border content-stretch flex flex-col items-start justify-start p-[8px] relative rounded-[8px] shrink-0 size-[190px]" data-name="Mobile Tile">
      <Options3 />
      <Avatar7 />
      <NameStatus3 />
    </div>
  );
}

function Component2() {
  return (
    <div className="content-stretch flex items-center justify-between relative shrink-0 w-[396px]" data-name="2">
      <MobileTile2 />
      <MobileTile3 />
    </div>
  );
}

function Options4() {
  return <div className="content-stretch flex items-center justify-between rounded-[24px] shrink-0 w-full" data-name="Options" />;
}

function Avatar8() {
  return (
    <div className="bg-[rgba(254,55,204,0.16)] relative rounded-[9999px] shrink-0 size-20" data-name="Avatar">
      <div className="content-stretch flex flex-col items-center justify-center overflow-clip relative size-20">
        <div className="font-['Geist:Medium',_sans-serif] font-medium leading-[0] relative shrink-0 text-[32px] text-[rgba(255,211,236,0.99)] text-center text-nowrap tracking-[-0.04px]">
          <p className="leading-[26px] whitespace-pre">SO</p>
        </div>
      </div>
      <div aria-hidden="true" className="absolute border-4 border-[rgba(0,0,0,0.43)] border-solid inset-0 pointer-events-none rounded-[9999px]" />
    </div>
  );
}

function Avatar9() {
  return (
    <div className="basis-0 content-stretch flex grow items-center justify-center min-h-px min-w-px relative shrink-0 w-full" data-name="Avatar">
      <Avatar8 />
    </div>
  );
}

function MicOff3() {
  return (
    <div className="relative shrink-0 size-5" data-name="Mic Off">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="Mic Off">
          <path d={svgPaths.p40a9cc0} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function MicStatus4() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Mic Status">
      <MicOff3 />
    </div>
  );
}

function NameStatus4() {
  return (
    <div className="bg-[rgba(30,30,30,0.6)] box-border content-stretch flex gap-2 h-[30px] items-center justify-start px-2 py-1 relative rounded-[8px] shrink-0" data-name="name & Status">
      <MicStatus4 />
      <div className="font-['Open_Sans:SemiBold',_sans-serif] font-semibold leading-[0] overflow-ellipsis overflow-hidden relative shrink-0 text-[16px] text-nowrap text-white tracking-[-0.48px]" style={{ fontVariationSettings: "'wdth' 100" }}>
        <p className="[text-overflow:inherit] leading-[normal] overflow-inherit whitespace-pre">Soliman</p>
      </div>
    </div>
  );
}

function MobileTile4() {
  return (
    <div className="backdrop-blur-[4.455px] backdrop-filter bg-[rgba(0,0,0,0.5)] box-border content-stretch flex flex-col items-start justify-start p-[8px] relative rounded-[8px] shrink-0 size-[190px]" data-name="Mobile Tile">
      <Options4 />
      <Avatar9 />
      <NameStatus4 />
    </div>
  );
}

function Options5() {
  return <div className="content-stretch flex items-center justify-between rounded-[24px] shrink-0 w-full" data-name="Options" />;
}

function Avatar10() {
  return (
    <div className="bg-[rgba(255,255,255,0.07)] relative rounded-[9999px] shrink-0 size-20" data-name="Avatar">
      <div className="content-stretch flex flex-col items-center justify-center overflow-clip relative size-20">
        <div className="font-['Geist:Medium',_sans-serif] font-medium leading-[0] relative shrink-0 text-[#eeeeee] text-[32px] text-center text-nowrap tracking-[-0.04px]">
          <p className="leading-[26px] whitespace-pre">AK</p>
        </div>
      </div>
      <div aria-hidden="true" className="absolute border-4 border-[rgba(0,0,0,0.43)] border-solid inset-0 pointer-events-none rounded-[9999px]" />
    </div>
  );
}

function Avatar11() {
  return (
    <div className="basis-0 content-stretch flex grow items-center justify-center min-h-px min-w-px relative shrink-0 w-full" data-name="Avatar">
      <Avatar10 />
    </div>
  );
}

function MicOff4() {
  return (
    <div className="relative shrink-0 size-5" data-name="Mic Off">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 20 20">
        <g id="Mic Off">
          <path d={svgPaths.p40a9cc0} fill="var(--fill-0, white)" id="Shape" />
        </g>
      </svg>
    </div>
  );
}

function MicStatus5() {
  return (
    <div className="content-stretch flex items-center justify-start relative shrink-0" data-name="Mic Status">
      <MicOff4 />
    </div>
  );
}

function NameStatus5() {
  return (
    <div className="bg-[rgba(30,30,30,0.6)] box-border content-stretch flex gap-2 h-[30px] items-center justify-start px-2 py-1 relative rounded-[8px] shrink-0" data-name="name & Status">
      <MicStatus5 />
      <div className="font-['Open_Sans:SemiBold',_sans-serif] font-semibold leading-[0] overflow-ellipsis overflow-hidden relative shrink-0 text-[16px] text-nowrap text-white tracking-[-0.48px]" style={{ fontVariationSettings: "'wdth' 100" }}>
        <p className="[text-overflow:inherit] leading-[normal] overflow-inherit whitespace-pre">Amr K.</p>
      </div>
    </div>
  );
}

function MobileTile5() {
  return (
    <div className="backdrop-blur-[4.455px] backdrop-filter bg-[rgba(0,0,0,0.5)] box-border content-stretch flex flex-col items-start justify-start p-[8px] relative rounded-[8px] shrink-0 size-[190px]" data-name="Mobile Tile">
      <Options5 />
      <Avatar11 />
      <NameStatus5 />
    </div>
  );
}

function Component3() {
  return (
    <div className="content-stretch flex h-[190px] items-center justify-between relative shrink-0 w-[396px]" data-name="3">
      <MobileTile4 />
      <MobileTile5 />
    </div>
  );
}

function MainFrame() {
  return (
    <div className="absolute box-border content-stretch flex flex-col gap-4 h-[624px] items-center justify-center left-0 p-[16px] top-[138px] w-[430px]" data-name="Main Frame">
      <Component1 />
      <Component2 />
      <Component3 />
    </div>
  );
}

function Pointing() {
  return (
    <div className="absolute h-[36.741px] translate-x-[-50%] translate-y-[-50%] w-[34.551px]" data-name="pointing" style={{ top: "calc(50% + 0.299px)", left: "calc(50% + 1.553px)" }}>
      <div className="absolute inset-[-2.45%_-8.16%_-14.21%_-8.16%]">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 41 43">
          <g filter="url(#filter0_d_1_12964)" id="pointing">
            <path d={svgPaths.p26af7d80} fill="var(--fill-0, white)" id="Shape" />
            <path clipRule="evenodd" d={svgPaths.p26af7d80} fillRule="evenodd" id="Shape_2" stroke="var(--stroke-0, black)" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.8" />
            <path d={svgPaths.p3c210200} fill="var(--fill-0, black)" id="Shape_3" />
            <path d={svgPaths.p21591200} fill="var(--fill-0, black)" id="Shape_4" />
            <path d={svgPaths.pb0d7e00} fill="var(--fill-0, black)" id="Shape_5" />
          </g>
          <defs>
            <filter colorInterpolationFilters="sRGB" filterUnits="userSpaceOnUse" height="42.8611" id="filter0_d_1_12964" width="40.1907" x="0.179997" y="0.0999801">
              <feFlood floodOpacity="0" result="BackgroundImageFix" />
              <feColorMatrix in="SourceAlpha" result="hardAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
              <feOffset dy="2.4" />
              <feGaussianBlur stdDeviation="0.96" />
              <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
              <feBlend in2="BackgroundImageFix" mode="normal" result="effect1_dropShadow_1_12964" />
              <feBlend in="SourceGraphic" in2="effect1_dropShadow_1_12964" mode="normal" result="shape" />
            </filter>
          </defs>
        </svg>
      </div>
    </div>
  );
}

function CursorPointer() {
  return (
    <div className="absolute left-[200px] overflow-clip size-[57.6px] top-[856px]" data-name="Cursor/Pointer">
      <Pointing />
    </div>
  );
}

function HostOptionsAsModerator() {
  return (
    <div className="absolute bg-[#1e1e1e] h-[932px] left-0 rounded-[40px] shadow-[0px_4px_4px_0px_rgba(0,0,0,0.25)] top-0 w-[430px]" data-name="Host Options as Moderator" style={{ backgroundImage: "linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%), linear-gradient(4.9738e-13deg, rgba(6, 6, 6, 0) 85.032%, rgba(0, 0, 0, 0.6) 100%)" }}>
      <StatusBar />
      <TopNavBar />
      <TypesOfPages />
      <Version3Footer />
      <MainFrame />
      <CursorPointer />
    </div>
  );
}

export default function Chat1() {
  return (
    <div className="content-stretch flex flex-col gap-2.5 items-center justify-end relative size-full" data-name="Chat">
      <HostOptionsAsModerator />
    </div>
  );
}